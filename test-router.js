#!/usr/bin/env node

const http = require('http');

// 测试函数
async function testAPI(message, description) {
  console.log(`\n🧪 测试: ${description}`);
  console.log(`📝 消息: "${message}"`);
  
  const data = JSON.stringify({
    model: "claude-3-5-sonnet-20241022",
    messages: [
      {
        role: "user",
        content: message
      }
    ],
    max_tokens: 100
  });

  const options = {
    hostname: '127.0.0.1',
    port: 3456,
    path: '/v1/messages',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer your-secret-key',
      'Content-Length': Buffer.byteLength(data)
    }
  };

  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const response = JSON.parse(responseData);
          if (response.content && response.content[0] && response.content[0].text) {
            console.log(`✅ 成功路由到模型: ${response.model}`);
            console.log(`📤 响应: ${response.content[0].text.substring(0, 100)}...`);
          } else if (response.error) {
            console.log(`❌ 错误: ${response.error.message}`);
          }
          resolve(response);
        } catch (error) {
          console.log(`❌ 解析响应失败: ${error.message}`);
          reject(error);
        }
      });
    });

    req.on('error', (error) => {
      console.log(`❌ 请求失败: ${error.message}`);
      reject(error);
    });

    req.write(data);
    req.end();
  });
}

// 主测试函数
async function runTests() {
  console.log('🚀 开始测试 Claude Code Router 路由功能\n');
  
  // 等待服务器启动
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  try {
    // 测试1: 包含 "qwen" 关键词，应该路由到魔搭社区
    await testAPI("请用qwen帮我写一个Python函数", "包含qwen关键词 - 应该路由到魔搭社区");
    
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 测试2: 包含 "claude" 关键词，应该路由到Claude
    await testAPI("请用claude帮我解释这段代码", "包含claude关键词 - 应该路由到Claude");
    
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 测试3: 不包含特定关键词，应该使用默认路由
    await testAPI("请帮我写一个JavaScript函数", "不包含特定关键词 - 应该使用默认路由");
    
  } catch (error) {
    console.error('测试过程中出现错误:', error);
  }
  
  console.log('\n✨ 测试完成！');
}

// 运行测试
runTests();
