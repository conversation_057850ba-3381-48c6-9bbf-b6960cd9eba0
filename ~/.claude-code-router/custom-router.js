/**
 * 自定义路由函数，根据用户输入的命令来选择不同的模型
 * 
 * @param {object} req - 来自 Claude Code 的请求对象，包含请求体
 * @param {object} config - 应用程序的配置对象
 * @returns {Promise<string|null>} - 返回 "provider,model_name" 字符串，或 null 使用默认路由
 */
module.exports = async function router(req, config) {
  try {
    // 获取用户消息内容
    const messages = req.body.messages || [];
    const userMessage = messages.find(m => m.role === 'user');
    
    if (!userMessage) {
      return null; // 使用默认路由
    }
    
    let content = '';
    
    // 处理不同类型的消息内容
    if (typeof userMessage.content === 'string') {
      content = userMessage.content.toLowerCase();
    } else if (Array.isArray(userMessage.content)) {
      // 提取文本内容
      const textParts = userMessage.content
        .filter(part => part.type === 'text')
        .map(part => part.text || '');
      content = textParts.join(' ').toLowerCase();
    }
    
    // 检查是否包含 "qwen" 关键词
    if (content.includes('qwen')) {
      console.log('检测到 qwen 关键词，路由到魔搭社区模型');
      return 'modelscope,Qwen/Qwen3-Coder-480B-A35B-Instruct';
    }
    
    // 检查是否包含 "claude" 关键词
    if (content.includes('claude')) {
      console.log('检测到 claude 关键词，路由到 Claude 模型');
      return 'anthropic,claude-3-5-sonnet-20241022';
    }
    
    // 默认情况下使用 Claude 模型
    return null; // 使用配置中的默认路由
    
  } catch (error) {
    console.error('自定义路由器错误:', error);
    return null; // 出错时使用默认路由
  }
};
