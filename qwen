#!/bin/bash

# Qwen 命令 - 通过 Claude Code Router 使用魔搭社区 Qwen 模型

if [ $# -eq 0 ]; then
    echo "🤖 Qwen - 魔搭社区 Qwen 模型助手"
    echo ""
    echo "用法:"
    echo "  ./qwen \"你的问题\""
    echo ""
    echo "示例:"
    echo "  ./qwen \"写一个Python Hello World程序\""
    echo "  ./qwen \"解释什么是机器学习\""
    echo ""
    exit 1
fi

# 获取用户输入并自动添加 qwen 关键词
USER_INPUT="请用qwen帮我: $1"

echo "🚀 正在向魔搭社区 Qwen 模型发送请求..."
echo "📝 问题: $1"
echo ""

# 检查 Claude Code Router 是否运行
if ! curl -s http://127.0.0.1:3456 > /dev/null 2>&1; then
    echo "❌ Claude Code Router 服务未运行"
    echo "请先启动服务:"
    echo "  cd /Users/<USER>/Desktop/学习资料/claude-code-router"
    echo "  node dist/cli.js start"
    exit 1
fi

# 发送请求
RESPONSE=$(curl -s -X POST "http://127.0.0.1:3456/v1/messages" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-secret-key" \
  -d "{
    \"model\": \"claude-3-5-sonnet-20241022\",
    \"messages\": [
      {
        \"role\": \"user\",
        \"content\": \"$USER_INPUT\"
      }
    ],
    \"max_tokens\": 2000
  }")

# 解析响应
echo "$RESPONSE" | python3 -c "
import json
import sys

try:
    data = json.load(sys.stdin)
    if 'content' in data and len(data['content']) > 0:
        print('🤖 Qwen 回答:')
        print('=' * 60)
        print(data['content'][0]['text'])
        print('=' * 60)
        print(f'📊 模型: {data.get(\"model\", \"未知\")}')
        usage = data.get('usage', {})
        if usage:
            print(f'📈 Token 使用: 输入 {usage.get(\"input_tokens\", \"未知\")}, 输出 {usage.get(\"output_tokens\", \"未知\")}')
    elif 'error' in data:
        print('❌ 错误:', data['error'].get('message', '未知错误'))
        print('详细信息:', json.dumps(data, indent=2, ensure_ascii=False))
    else:
        print('❌ 未收到有效响应')
        print('原始响应:', json.dumps(data, indent=2, ensure_ascii=False))
except json.JSONDecodeError as e:
    print('❌ JSON 解析错误:', e)
    print('原始响应:')
    for line in sys.stdin:
        print(repr(line))
except Exception as e:
    print(f'❌ 处理响应时出错: {e}')
"

echo ""
echo "✨ 完成！"
