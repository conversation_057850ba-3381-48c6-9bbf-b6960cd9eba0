#!/bin/bash

# 设置 qwen 别名的脚本

echo "🔧 正在设置 qwen 命令别名..."

# 获取当前脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 创建别名命令
ALIAS_COMMAND="alias qwen='python3 \"$SCRIPT_DIR/qwen_chat.py\"'"

# 检测使用的 shell
if [[ "$SHELL" == *"zsh"* ]]; then
    SHELL_CONFIG="$HOME/.zshrc"
    SHELL_NAME="zsh"
elif [[ "$SHELL" == *"bash"* ]]; then
    SHELL_CONFIG="$HOME/.bashrc"
    SHELL_NAME="bash"
else
    echo "⚠️ 未检测到支持的 shell，请手动添加别名"
    echo "请在你的 shell 配置文件中添加以下行："
    echo "$ALIAS_COMMAND"
    exit 1
fi

# 检查别名是否已存在
if grep -q "alias qwen=" "$SHELL_CONFIG" 2>/dev/null; then
    echo "⚠️ qwen 别名已存在，正在更新..."
    # 删除旧的别名
    sed -i.bak '/alias qwen=/d' "$SHELL_CONFIG"
fi

# 添加新的别名
echo "" >> "$SHELL_CONFIG"
echo "# Qwen Chat 别名 - 通过 Claude Code Router 使用魔搭社区 Qwen 模型" >> "$SHELL_CONFIG"
echo "$ALIAS_COMMAND" >> "$SHELL_CONFIG"

echo "✅ 别名已添加到 $SHELL_CONFIG"
echo ""
echo "🔄 请运行以下命令来重新加载配置："
echo "  source $SHELL_CONFIG"
echo ""
echo "或者重新打开终端"
echo ""
echo "🎯 然后你就可以使用以下命令："
echo "  qwen \"你的问题\""
echo "  qwen -i  # 交互模式"
echo ""
echo "示例："
echo "  qwen \"写一个Python排序算法\""
echo "  qwen \"解释什么是机器学习\""
