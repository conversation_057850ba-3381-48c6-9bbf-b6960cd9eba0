# Claude Code Router 配置完成

## 🎉 配置成功！

你的 Claude Code Router 已经成功配置并运行，现在可以根据输入的关键词自动路由到不同的模型：

- **输入包含 "qwen"** → 路由到魔搭社区的 `Qwen/Qwen3-Coder-480B-A35B-Instruct` 模型
- **输入包含 "claude"** → 路由到 Claude 模型（需要有效的 Anthropic API 密钥）
- **其他情况** → 使用默认路由（Claude 模型）

## 📋 配置信息

### 魔搭社区配置
- **API 端点**: `https://api-inference.modelscope.cn/v1/`
- **API 密钥**: `ms-1652c7f0-f3de-4ad2-826f-591b4e4b37e2`
- **模型**: `Qwen/Qwen3-Coder-480B-A35B-Instruct`
- **状态**: ✅ 已测试，工作正常

### 配置文件位置
- **主配置**: `/Users/<USER>/.claude-code-router/config.json`
- **自定义路由器**: `/Users/<USER>/.claude-code-router/custom-router.js`

## 🚀 使用方法

### 1. 启动服务
```bash
cd /Users/<USER>/Desktop/学习资料/claude-code-router
node dist/cli.js start
```

### 2. 使用示例

#### 使用魔搭社区 Qwen 模型
```bash
# 任何包含 "qwen" 的请求都会路由到魔搭社区
curl -X POST "http://127.0.0.1:3456/v1/messages" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-secret-key" \
  -d '{
    "model": "claude-3-5-sonnet-20241022",
    "messages": [{"role": "user", "content": "请用qwen帮我写代码"}],
    "max_tokens": 1000
  }'
```

#### 使用 Claude 模型
```bash
# 包含 "claude" 的请求会路由到 Claude（需要有效 API 密钥）
curl -X POST "http://127.0.0.1:3456/v1/messages" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-secret-key" \
  -d '{
    "model": "claude-3-5-sonnet-20241022",
    "messages": [{"role": "user", "content": "请用claude帮我分析代码"}],
    "max_tokens": 1000
  }'
```

## 🔧 管理命令

```bash
# 启动服务
node dist/cli.js start

# 停止服务
node dist/cli.js stop

# 重启服务
node dist/cli.js restart

# 查看状态
node dist/cli.js status
```

## 📝 测试结果

✅ **魔搭社区路由测试通过**
- 关键词检测：正常
- API 调用：成功
- 响应格式：正确

❌ **Claude 路由测试**
- 关键词检测：正常
- API 调用：失败（需要有效的 Anthropic API 密钥）

## 🛠️ 自定义路由逻辑

路由器会检查用户消息中的关键词：

1. **包含 "qwen"** → `modelscope,Qwen/Qwen3-Coder-480B-A35B-Instruct`
2. **包含 "claude"** → `anthropic,claude-3-5-sonnet-20241022`
3. **其他情况** → 使用默认配置

你可以修改 `/Users/<USER>/.claude-code-router/custom-router.js` 来调整路由逻辑。

## 📊 服务器日志

服务器会输出详细的路由日志，包括：
- 关键词检测结果
- 选择的模型
- API 请求详情
- 响应转换过程

## 🎯 下一步

1. **如果需要使用 Claude 模型**：在配置文件中添加有效的 Anthropic API 密钥
2. **添加更多模型**：在 `Providers` 数组中添加其他模型提供商
3. **自定义路由规则**：修改 `custom-router.js` 添加更复杂的路由逻辑

## 🔍 故障排除

如果遇到问题：
1. 检查服务器是否正在运行：`node dist/cli.js status`
2. 查看服务器日志输出
3. 确认配置文件格式正确
4. 验证 API 密钥有效性
