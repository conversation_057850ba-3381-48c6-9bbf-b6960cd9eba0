#!/usr/bin/env python3
"""
<PERSON><PERSON> Chat - 通过 Claude Code Router 使用魔搭社区 Qwen 模型的便捷工具
"""

import requests
import json
import sys
import argparse

def ask_qwen(question, max_tokens=2000):
    """向 Qwen 模型发送问题"""
    url = 'http://127.0.0.1:3456/v1/messages'
    headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer your-secret-key'
    }
    
    # 自动添加 qwen 关键词以确保路由到正确的模型
    content = f"请用qwen帮我: {question}"
    
    data = {
        'model': 'claude-3-5-sonnet-20241022',
        'messages': [
            {
                'role': 'user',
                'content': content
            }
        ],
        'max_tokens': max_tokens
    }
    
    try:
        print(f"🚀 正在向魔搭社区 Qwen 模型发送请求...")
        print(f"📝 问题: {question}")
        print()
        
        response = requests.post(url, headers=headers, json=data, timeout=30)
        result = response.json()
        
        if 'content' in result and len(result['content']) > 0:
            print('🤖 Qwen 回答:')
            print('=' * 60)
            print(result['content'][0]['text'])
            print('=' * 60)
            print(f"📊 模型: {result.get('model', '未知')}")
            
            usage = result.get('usage', {})
            if usage:
                input_tokens = usage.get('input_tokens', '未知')
                output_tokens = usage.get('output_tokens', '未知')
                print(f"📈 Token 使用: 输入 {input_tokens}, 输出 {output_tokens}")
            
            return True
            
        elif 'error' in result:
            print(f"❌ 错误: {result['error'].get('message', '未知错误')}")
            print("详细信息:", json.dumps(result, indent=2, ensure_ascii=False))
            return False
            
        else:
            print("❌ 未收到有效响应")
            print("原始响应:", json.dumps(result, indent=2, ensure_ascii=False))
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败: Claude Code Router 服务未运行")
        print("请先启动服务:")
        print("  cd /Users/<USER>/Desktop/学习资料/claude-code-router")
        print("  node dist/cli.js start")
        return False
        
    except requests.exceptions.Timeout:
        print("❌ 请求超时，请稍后重试")
        return False
        
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False

def interactive_mode():
    """交互模式"""
    print("🤖 Qwen Chat - 交互模式")
    print("输入 'quit' 或 'exit' 退出")
    print("=" * 50)
    
    while True:
        try:
            question = input("\n💬 请输入你的问题: ").strip()
            
            if question.lower() in ['quit', 'exit', 'q']:
                print("👋 再见！")
                break
                
            if not question:
                print("⚠️ 请输入有效的问题")
                continue
                
            ask_qwen(question)
            
        except KeyboardInterrupt:
            print("\n👋 再见！")
            break
        except EOFError:
            print("\n👋 再见！")
            break

def main():
    parser = argparse.ArgumentParser(
        description='Qwen Chat - 通过 Claude Code Router 使用魔搭社区 Qwen 模型',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例:
  python3 qwen_chat.py "写一个Python Hello World程序"
  python3 qwen_chat.py "解释什么是机器学习"
  python3 qwen_chat.py -i  # 交互模式
        """
    )
    
    parser.add_argument('question', nargs='?', help='要问的问题')
    parser.add_argument('-i', '--interactive', action='store_true', help='启动交互模式')
    parser.add_argument('-t', '--tokens', type=int, default=2000, help='最大 token 数量 (默认: 2000)')
    
    args = parser.parse_args()
    
    if args.interactive:
        interactive_mode()
    elif args.question:
        success = ask_qwen(args.question, args.tokens)
        sys.exit(0 if success else 1)
    else:
        # 如果没有提供参数，显示帮助并进入交互模式
        parser.print_help()
        print("\n" + "=" * 50)
        interactive_mode()

if __name__ == '__main__':
    main()
