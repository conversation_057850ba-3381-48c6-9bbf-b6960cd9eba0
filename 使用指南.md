# 🤖 Claude Code Router 使用指南

## ❓ 为什么输入 `qwen` 会显示 "command not found"？

这是正常的！`qwen` 不是一个独立的命令行工具。Claude Code Router 的工作原理是：

1. **作为代理服务器**：拦截发送给 Claude 的请求
2. **智能路由**：根据消息内容中的关键词决定使用哪个模型
3. **透明转换**：将请求转发到相应的 AI 服务

## 🚀 正确的使用方式

### 方式一：直接 API 调用（推荐用于测试）

```bash
# 使用魔搭社区 Qwen 模型（包含 "qwen" 关键词）
curl -X POST "http://127.0.0.1:3456/v1/messages" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-secret-key" \
  -d '{
    "model": "claude-3-5-sonnet-20241022",
    "messages": [
      {
        "role": "user",
        "content": "请用qwen帮我写一个Python Hello World程序"
      }
    ],
    "max_tokens": 1000
  }'
```

### 方式二：使用便捷脚本

我为你创建了一个便捷脚本，在项目目录下运行：

```bash
# 进入项目目录
cd /Users/<USER>/Desktop/学习资料/claude-code-router

# 使用便捷脚本
python3 -c "
import requests
import json
import sys

def ask_qwen(question):
    url = 'http://127.0.0.1:3456/v1/messages'
    headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer your-secret-key'
    }
    data = {
        'model': 'claude-3-5-sonnet-20241022',
        'messages': [
            {
                'role': 'user',
                'content': f'请用qwen帮我: {question}'
            }
        ],
        'max_tokens': 2000
    }
    
    try:
        response = requests.post(url, headers=headers, json=data)
        result = response.json()
        
        if 'content' in result and len(result['content']) > 0:
            print('🤖 Qwen 回答:')
            print('=' * 60)
            print(result['content'][0]['text'])
            print('=' * 60)
            print(f'📊 模型: {result.get(\"model\", \"未知\")}')
            usage = result.get('usage', {})
            if usage:
                print(f'📈 Token 使用: 输入 {usage.get(\"input_tokens\", \"未知\")}, 输出 {usage.get(\"output_tokens\", \"未知\")}')
        else:
            print('❌ 错误:', result.get('error', {}).get('message', '未知错误'))
    except Exception as e:
        print(f'❌ 请求失败: {e}')

if len(sys.argv) > 1:
    ask_qwen(' '.join(sys.argv[1:]))
else:
    question = input('请输入你的问题: ')
    ask_qwen(question)
" "写一个Python Hello World程序"
```

### 方式三：创建别名（推荐）

在你的 shell 配置文件中添加别名：

```bash
# 编辑 ~/.zshrc 文件
echo 'alias qwen="python3 -c \"
import requests
import json
import sys

def ask_qwen(question):
    url = \\\"http://127.0.0.1:3456/v1/messages\\\"
    headers = {
        \\\"Content-Type\\\": \\\"application/json\\\",
        \\\"Authorization\\\": \\\"Bearer your-secret-key\\\"
    }
    data = {
        \\\"model\\\": \\\"claude-3-5-sonnet-20241022\\\",
        \\\"messages\\\": [
            {
                \\\"role\\\": \\\"user\\\",
                \\\"content\\\": f\\\"请用qwen帮我: {question}\\\"
            }
        ],
        \\\"max_tokens\\\": 2000
    }
    
    try:
        response = requests.post(url, headers=headers, json=data)
        result = response.json()
        
        if \\\"content\\\" in result and len(result[\\\"content\\\"]) > 0:
            print(\\\"🤖 Qwen 回答:\\\")
            print(\\\"=\\\" * 60)
            print(result[\\\"content\\\"][0][\\\"text\\\"])
            print(\\\"=\\\" * 60)
            print(f\\\"📊 模型: {result.get(\\\\\\\"model\\\\\\\", \\\\\\\"未知\\\\\\\")}\\\")
        else:
            print(\\\"❌ 错误:\\\", result.get(\\\"error\\\", {}).get(\\\"message\\\", \\\"未知错误\\\"))
    except Exception as e:
        print(f\\\"❌ 请求失败: {e}\\\")

ask_qwen(\\\" \\\".join(sys.argv[1:]))
\"" >> ~/.zshrc

# 重新加载配置
source ~/.zshrc
```

## 📝 使用示例

### 1. 测试魔搭社区 Qwen 模型

```bash
# 方式一：直接 curl
curl -X POST "http://127.0.0.1:3456/v1/messages" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-secret-key" \
  -d '{
    "model": "claude-3-5-sonnet-20241022",
    "messages": [{"role": "user", "content": "请用qwen写一个排序算法"}],
    "max_tokens": 1000
  }'
```

### 2. 测试 Claude 模型路由

```bash
curl -X POST "http://127.0.0.1:3456/v1/messages" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-secret-key" \
  -d '{
    "model": "claude-3-5-sonnet-20241022",
    "messages": [{"role": "user", "content": "请用claude解释机器学习"}],
    "max_tokens": 1000
  }'
```

### 3. 测试默认路由

```bash
curl -X POST "http://127.0.0.1:3456/v1/messages" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-secret-key" \
  -d '{
    "model": "claude-3-5-sonnet-20241022",
    "messages": [{"role": "user", "content": "解释什么是编程"}],
    "max_tokens": 1000
  }'
```

## 🔧 服务管理

```bash
# 进入项目目录
cd /Users/<USER>/Desktop/学习资料/claude-code-router

# 启动服务
node dist/cli.js start

# 查看状态
node dist/cli.js status

# 停止服务
node dist/cli.js stop

# 重启服务
node dist/cli.js restart
```

## 📊 路由规则

- **包含 "qwen"** → 魔搭社区 `Qwen/Qwen3-Coder-480B-A35B-Instruct`
- **包含 "claude"** → Claude 模型（需要 API 密钥）
- **其他情况** → 默认路由（Claude 模型）

## 🎯 快速测试

运行这个命令来快速测试 Qwen 模型：

```bash
cd /Users/<USER>/Desktop/学习资料/claude-code-router

python3 -c "
import requests
import json

response = requests.post(
    'http://127.0.0.1:3456/v1/messages',
    headers={
        'Content-Type': 'application/json',
        'Authorization': 'Bearer your-secret-key'
    },
    json={
        'model': 'claude-3-5-sonnet-20241022',
        'messages': [{'role': 'user', 'content': '请用qwen写一个简单的Python函数'}],
        'max_tokens': 1000
    }
)

result = response.json()
if 'content' in result:
    print('🤖 Qwen 回答:')
    print('=' * 50)
    print(result['content'][0]['text'])
    print('=' * 50)
    print(f'模型: {result.get(\"model\")}')
else:
    print('错误:', result)
"
```

这样你就可以正确使用魔搭社区的 Qwen 模型了！🎉
