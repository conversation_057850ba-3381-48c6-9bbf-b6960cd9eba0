#!/bin/bash

# Claude Code Router - Qwen Helper Script
# 这个脚本帮助你快速使用 qwen 模型

# 检查参数
if [ $# -eq 0 ]; then
    echo "🤖 Qwen Helper - 使用魔搭社区 Qwen 模型"
    echo ""
    echo "用法:"
    echo "  ./qwen-helper.sh \"你的问题\""
    echo ""
    echo "示例:"
    echo "  ./qwen-helper.sh \"请用qwen帮我写一个Python Hello World程序\""
    echo "  ./qwen-helper.sh \"qwen，请解释什么是机器学习\""
    echo ""
    echo "注意: 问题中需要包含 'qwen' 关键词才会路由到魔搭社区模型"
    exit 1
fi

# 获取用户输入
USER_INPUT="$1"

# 检查是否包含 qwen 关键词
if [[ ! "$USER_INPUT" == *"qwen"* ]]; then
    echo "⚠️  警告: 你的输入中没有包含 'qwen' 关键词"
    echo "   为了确保路由到魔搭社区模型，建议在问题中包含 'qwen'"
    echo ""
    echo "是否继续? (y/N)"
    read -r response
    if [[ ! "$response" =~ ^[Yy]$ ]]; then
        echo "已取消"
        exit 0
    fi
    # 自动添加 qwen 关键词
    USER_INPUT="请用qwen帮我: $USER_INPUT"
    echo "✅ 已自动添加 qwen 关键词: $USER_INPUT"
fi

echo "🚀 正在向魔搭社区 Qwen 模型发送请求..."
echo "📝 问题: $USER_INPUT"
echo ""

# 发送请求到 Claude Code Router
curl -s -X POST "http://127.0.0.1:3456/v1/messages" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-secret-key" \
  -d "{
    \"model\": \"claude-3-5-sonnet-20241022\",
    \"messages\": [
      {
        \"role\": \"user\",
        \"content\": \"$USER_INPUT\"
      }
    ],
    \"max_tokens\": 2000
  }" | python3 -c "
import json
import sys

try:
    data = json.load(sys.stdin)
    if 'content' in data and len(data['content']) > 0:
        print('🤖 Qwen 回答:')
        print('=' * 50)
        print(data['content'][0]['text'])
        print('=' * 50)
        print(f'📊 模型: {data.get(\"model\", \"未知\")}')
        print(f'📈 Token 使用: 输入 {data.get(\"usage\", {}).get(\"input_tokens\", \"未知\")}, 输出 {data.get(\"usage\", {}).get(\"output_tokens\", \"未知\")}')
    elif 'error' in data:
        print('❌ 错误:', data['error'].get('message', '未知错误'))
    else:
        print('❌ 未收到有效响应')
except json.JSONDecodeError:
    print('❌ 响应格式错误')
except Exception as e:
    print(f'❌ 处理响应时出错: {e}')
"

echo ""
echo "✨ 完成！"
